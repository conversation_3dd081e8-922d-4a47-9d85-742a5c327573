// timed-playback.service.ts
import { Injectable, NgZone } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { LogService } from './log.service';
import { PlaylistItem } from '../models/playlist.model';

export interface TimerState {
  isActive: boolean;
  isPaused: boolean;
  currentItem: PlaylistItem | null;
  duration: number; // Total duration in seconds
  elapsed: number; // Elapsed time in seconds
  remaining: number; // Remaining time in seconds
  progress: number; // Progress percentage (0-100)
  startTime: number; // Timestamp when timer started
  pausedTime: number; // Total paused time in milliseconds
}

export interface TimerConfig {
  item: PlaylistItem;
  duration?: number; // Override item duration
  onComplete?: () => void;
  onProgress?: (progress: number, remaining: number) => void;
  onPause?: () => void;
  onResume?: () => void;
}

@Injectable({
  providedIn: 'root'
})
export class TimedPlaybackService {
  private timerStateSubject = new BehaviorSubject<TimerState>({
    isActive: false,
    isPaused: false,
    currentItem: null,
    duration: 0,
    elapsed: 0,
    remaining: 0,
    progress: 0,
    startTime: 0,
    pausedTime: 0
  });

  private timerCompleted$ = new Subject<PlaylistItem>();
  private timerProgress$ = new Subject<{ item: PlaylistItem; progress: number; remaining: number }>();

  private currentTimer: any = null;
  private progressTimer: any = null;
  private currentConfig: TimerConfig | null = null;
  private pauseStartTime = 0;
  private totalPausedTime = 0;

  constructor(
    private logService: LogService,
    private zone: NgZone
  ) {}

  /**
   * Get current timer state
   */
  getTimerState(): Observable<TimerState> {
    return this.timerStateSubject.asObservable();
  }

  /**
   * Get timer completion events
   */
  getTimerCompleted(): Observable<PlaylistItem> {
    return this.timerCompleted$.asObservable();
  }

  /**
   * Get timer progress events
   */
  getTimerProgress(): Observable<{ item: PlaylistItem; progress: number; remaining: number }> {
    return this.timerProgress$.asObservable();
  }

  /**
   * Start a timer for a playlist item
   */
  startTimer(config: TimerConfig): void {
    this.stopTimer();
    
    const duration = config.duration || config.item.duration || 10;
    this.currentConfig = config;
    
    this.logService.info(`Starting timer for item: ${config.item.name}, duration: ${duration}s`);
    
    const startTime = Date.now();
    this.totalPausedTime = 0;
    
    this.updateTimerState({
      isActive: true,
      isPaused: false,
      currentItem: config.item,
      duration,
      elapsed: 0,
      remaining: duration,
      progress: 0,
      startTime,
      pausedTime: 0
    });

    // Start the main timer
    this.currentTimer = setTimeout(() => {
      this.zone.run(() => {
        this.onTimerComplete();
      });
    }, duration * 1000);

    // Start progress tracking
    this.startProgressTracking();
  }

  /**
   * Pause the current timer
   */
  pauseTimer(): void {
    if (!this.currentConfig || !this.timerStateSubject.value.isActive || this.timerStateSubject.value.isPaused) {
      return;
    }

    this.logService.info(`Pausing timer for item: ${this.currentConfig.item.name}`);
    
    this.pauseStartTime = Date.now();
    this.stopProgressTracking();
    
    if (this.currentTimer) {
      clearTimeout(this.currentTimer);
      this.currentTimer = null;
    }

    this.updateTimerState({
      isPaused: true
    });

    if (this.currentConfig.onPause) {
      this.currentConfig.onPause();
    }
  }

  /**
   * Resume the paused timer
   */
  resumeTimer(): void {
    if (!this.currentConfig || !this.timerStateSubject.value.isActive || !this.timerStateSubject.value.isPaused) {
      return;
    }

    this.logService.info(`Resuming timer for item: ${this.currentConfig.item.name}`);
    
    // Calculate paused duration
    const pausedDuration = Date.now() - this.pauseStartTime;
    this.totalPausedTime += pausedDuration;
    
    const currentState = this.timerStateSubject.value;
    const remainingTime = currentState.remaining * 1000; // Convert to milliseconds
    
    // Restart the timer with remaining time
    this.currentTimer = setTimeout(() => {
      this.zone.run(() => {
        this.onTimerComplete();
      });
    }, remainingTime);

    this.updateTimerState({
      isPaused: false,
      pausedTime: this.totalPausedTime
    });

    this.startProgressTracking();

    if (this.currentConfig.onResume) {
      this.currentConfig.onResume();
    }
  }

  /**
   * Stop the current timer
   */
  stopTimer(): void {
    if (this.currentTimer) {
      clearTimeout(this.currentTimer);
      this.currentTimer = null;
    }

    this.stopProgressTracking();
    
    this.updateTimerState({
      isActive: false,
      isPaused: false,
      currentItem: null,
      duration: 0,
      elapsed: 0,
      remaining: 0,
      progress: 0,
      startTime: 0,
      pausedTime: 0
    });

    this.currentConfig = null;
    this.totalPausedTime = 0;
    this.pauseStartTime = 0;
  }

  /**
   * Skip to the end of current timer
   */
  skipTimer(): void {
    if (this.currentConfig && this.timerStateSubject.value.isActive) {
      this.logService.info(`Skipping timer for item: ${this.currentConfig.item.name}`);
      this.onTimerComplete();
    }
  }

  /**
   * Get remaining time for current timer
   */
  getRemainingTime(): number {
    return this.timerStateSubject.value.remaining;
  }

  /**
   * Get progress percentage for current timer
   */
  getProgress(): number {
    return this.timerStateSubject.value.progress;
  }

  /**
   * Check if timer is currently active
   */
  isTimerActive(): boolean {
    return this.timerStateSubject.value.isActive;
  }

  /**
   * Check if timer is currently paused
   */
  isTimerPaused(): boolean {
    return this.timerStateSubject.value.isPaused;
  }

  private startProgressTracking(): void {
    this.stopProgressTracking();
    
    this.progressTimer = setInterval(() => {
      this.zone.run(() => {
        this.updateProgress();
      });
    }, 100); // Update every 100ms for smooth progress
  }

  private stopProgressTracking(): void {
    if (this.progressTimer) {
      clearInterval(this.progressTimer);
      this.progressTimer = null;
    }
  }

  private updateProgress(): void {
    if (!this.currentConfig || this.timerStateSubject.value.isPaused) {
      return;
    }

    const currentState = this.timerStateSubject.value;
    const now = Date.now();
    const totalElapsed = (now - currentState.startTime - this.totalPausedTime) / 1000; // Convert to seconds
    const elapsed = Math.min(totalElapsed, currentState.duration);
    const remaining = Math.max(currentState.duration - elapsed, 0);
    const progress = currentState.duration > 0 ? (elapsed / currentState.duration) * 100 : 0;

    this.updateTimerState({
      elapsed,
      remaining,
      progress: Math.min(progress, 100)
    });

    // Emit progress event
    this.timerProgress$.next({
      item: this.currentConfig.item,
      progress: Math.min(progress, 100),
      remaining
    });

    // Call progress callback if provided
    if (this.currentConfig.onProgress) {
      this.currentConfig.onProgress(Math.min(progress, 100), remaining);
    }
  }

  private onTimerComplete(): void {
    if (!this.currentConfig) return;

    const item = this.currentConfig.item;
    this.logService.info(`Timer completed for item: ${item.name}`);

    // Call completion callback if provided
    if (this.currentConfig.onComplete) {
      this.currentConfig.onComplete();
    }

    // Emit completion event
    this.timerCompleted$.next(item);

    // Update final state
    this.updateTimerState({
      elapsed: this.timerStateSubject.value.duration,
      remaining: 0,
      progress: 100
    });

    // Clean up
    this.stopTimer();
  }

  private updateTimerState(updates: Partial<TimerState>): void {
    const currentState = this.timerStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.timerStateSubject.next(newState);
  }
}
