// enhanced-playback.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, switchMap, catchError, tap } from 'rxjs';
import { Playlist, PlaylistItem } from '../models/playlist.model';
import { PlayerState } from '../models/player-state.model';
import { LogService } from './log.service';
import { ContentSyncService } from './content-sync.service';
import { MediaManagementService, MediaDownloadProgress } from './media-management.service';
import { SupabaseApiService } from './supabase-api.service';

export interface EnhancedPlayerState extends PlayerState {
  downloadProgress?: MediaDownloadProgress | null;
  isDownloading: boolean;
  mediaReadiness: 'unknown' | 'checking' | 'ready' | 'partial' | 'missing';
  lastSyncTime?: Date;
}

@Injectable({
  providedIn: 'root'
})
export class EnhancedPlaybackService {
  // Enhanced player state
  private playerStateSubject = new BehaviorSubject<EnhancedPlayerState>({
    isPlaying: false,
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : false,
    currentPlaylistId: null,
    currentPlaylistName: '',
    currentItemIndex: 0,
    totalItems: 0,
    lastUpdated: new Date(),
    downloadProgress: null,
    isDownloading: false,
    mediaReadiness: 'unknown'
  });

  // Current playlist and playback state
  private currentPlaylist: Playlist | null = null;
  private currentIndex = 0;
  private isPlaying = false;

  // Observables
  public playerState$ = this.playerStateSubject.asObservable();
  public currentItem$ = new BehaviorSubject<PlaylistItem | null>(null);
  public playbackError$ = new BehaviorSubject<string | null>(null);

  constructor(
    private logService: LogService,
    private contentSyncService: ContentSyncService,
    private mediaManagementService: MediaManagementService,
    private supabaseApi: SupabaseApiService
  ) {
    // Subscribe to download progress
    this.mediaManagementService.getDownloadProgress().subscribe(progress => {
      this.updatePlayerState({
        downloadProgress: progress,
        isDownloading: progress ? !progress.isComplete : false
      });
    });
  }

  /**
   * Load and prepare a playlist with enhanced downloading
   */
  loadPlaylistWithDownload(playlistId: string, forceDownload = false): Observable<boolean> {
    if (!playlistId) {
      this.playbackError$.next('No playlist ID provided');
      return of(false);
    }

    this.logService.info(`Loading playlist with download: ${playlistId}`);
    
    // Update state to show loading
    this.updatePlayerState({
      currentPlaylistId: playlistId,
      currentPlaylistName: 'Loading...',
      mediaReadiness: 'checking'
    });

    // First try to get playlist from cache
    return this.contentSyncService.getCachedPlaylist(playlistId).pipe(
      switchMap(cachedPlaylist => {
        if (cachedPlaylist && !forceDownload) {
          this.logService.info(`Using cached playlist: ${playlistId}`);
          return this.checkAndPreparePlaylist(cachedPlaylist);
        } else {
          // Fetch from network
          this.logService.info(`Fetching playlist from network: ${playlistId}`);
          return this.supabaseApi.getPlaylistById(playlistId).pipe(
            switchMap(playlist => {
              if (playlist) {
                // Cache the playlist
                this.contentSyncService.cachePlaylist(playlist);
                return this.checkAndPreparePlaylist(playlist);
              } else {
                throw new Error('Playlist not found');
              }
            })
          );
        }
      }),
      catchError(error => {
        this.logService.error(`Failed to load playlist: ${error.message}`);
        this.playbackError$.next(`Failed to load playlist: ${error.message}`);
        this.updatePlayerState({ mediaReadiness: 'missing' });
        return of(false);
      })
    );
  }

  /**
   * Check media readiness and download if needed
   */
  private checkAndPreparePlaylist(playlist: Playlist): Observable<boolean> {
    this.currentPlaylist = playlist;
    
    this.updatePlayerState({
      currentPlaylistName: playlist.name,
      totalItems: playlist.items.length,
      mediaReadiness: 'checking'
    });

    // Check if all media is cached
    return this.mediaManagementService.checkPlaylistMediaStatus(playlist).pipe(
      switchMap(status => {
        if (status.isFullyCached) {
          this.logService.info(`All media cached for playlist: ${playlist.name}`);
          this.updatePlayerState({ mediaReadiness: 'ready' });
          this.startPlayback();
          return of(true);
        } else {
          this.logService.info(`Downloading missing media for playlist: ${playlist.name}`);
          this.updatePlayerState({ mediaReadiness: 'partial' });
          return this.downloadPlaylistMedia(playlist);
        }
      }),
      catchError(error => {
        this.logService.error(`Error preparing playlist: ${error.message}`);
        this.playbackError$.next(`Error preparing playlist: ${error.message}`);
        this.updatePlayerState({ mediaReadiness: 'missing' });
        return of(false);
      })
    );
  }

  /**
   * Download all media for the current playlist
   */
  private downloadPlaylistMedia(playlist: Playlist): Observable<boolean> {
    return this.mediaManagementService.downloadPlaylistMedia(playlist).pipe(
      tap(progress => {
        if (progress.isComplete) {
          if (progress.hasErrors) {
            this.logService.warn(`Media download completed with errors for: ${playlist.name}`);
            this.updatePlayerState({ mediaReadiness: 'partial' });
          } else {
            this.logService.info(`Media download completed successfully for: ${playlist.name}`);
            this.updatePlayerState({ mediaReadiness: 'ready' });
          }
          this.startPlayback();
        }
      }),
      switchMap(progress => of(progress.isComplete && !progress.hasErrors)),
      catchError(error => {
        this.logService.error(`Media download failed: ${error.message}`);
        this.playbackError$.next(`Media download failed: ${error.message}`);
        this.updatePlayerState({ mediaReadiness: 'missing' });
        return of(false);
      })
    );
  }

  /**
   * Start playback of the current playlist
   */
  private startPlayback(): void {
    if (!this.currentPlaylist || this.currentPlaylist.items.length === 0) {
      this.playbackError$.next('No playlist items to play');
      return;
    }

    this.currentIndex = 0;
    this.isPlaying = true;
    
    this.updatePlayerState({
      isPlaying: true,
      currentItemIndex: 0,
      lastUpdated: new Date()
    });

    this.playCurrentItem();
  }

  /**
   * Play the current item
   */
  private playCurrentItem(): void {
    if (!this.currentPlaylist || this.currentIndex >= this.currentPlaylist.items.length) {
      return;
    }

    const item = this.currentPlaylist.items[this.currentIndex];
    this.logService.info(`Playing item: ${item.name} (${this.currentIndex + 1}/${this.currentPlaylist.items.length})`);
    
    this.currentItem$.next(item);
    
    this.updatePlayerState({
      currentItemIndex: this.currentIndex
    });
  }

  /**
   * Move to next item
   */
  skipToNext(): void {
    if (!this.currentPlaylist || !this.isPlaying) return;

    this.currentIndex = (this.currentIndex + 1) % this.currentPlaylist.items.length;
    this.playCurrentItem();
  }

  /**
   * Move to previous item
   */
  skipToPrevious(): void {
    if (!this.currentPlaylist || !this.isPlaying) return;

    this.currentIndex = this.currentIndex === 0 
      ? this.currentPlaylist.items.length - 1 
      : this.currentIndex - 1;
    this.playCurrentItem();
  }

  /**
   * Pause playback
   */
  pausePlayback(): void {
    this.isPlaying = false;
    this.updatePlayerState({ isPlaying: false });
    this.logService.info('Playback paused');
  }

  /**
   * Resume playback
   */
  resumePlayback(): void {
    this.isPlaying = true;
    this.updatePlayerState({ isPlaying: true });
    this.logService.info('Playback resumed');
  }

  /**
   * Stop playback
   */
  stopPlayback(): void {
    this.isPlaying = false;
    this.currentIndex = 0;
    this.currentItem$.next(null);
    
    this.updatePlayerState({
      isPlaying: false,
      currentItemIndex: 0
    });
    
    this.logService.info('Playback stopped');
  }

  /**
   * Restart current playlist
   */
  restartPlayback(): void {
    if (this.currentPlaylist) {
      this.currentIndex = 0;
      this.startPlayback();
    }
  }

  /**
   * Reload current playlist
   */
  reloadPlaylist(): void {
    if (this.playerStateSubject.value.currentPlaylistId) {
      this.loadPlaylistWithDownload(this.playerStateSubject.value.currentPlaylistId, true);
    }
  }

  /**
   * Get current player state
   */
  getCurrentState(): EnhancedPlayerState {
    return this.playerStateSubject.value;
  }

  /**
   * Get current playlist
   */
  getCurrentPlaylist(): Playlist | null {
    return this.currentPlaylist;
  }

  /**
   * Get current item
   */
  getCurrentItem(): PlaylistItem | null {
    return this.currentItem$.value;
  }

  /**
   * Clear any errors
   */
  clearError(): void {
    this.playbackError$.next(null);
  }

  /**
   * Update player state
   */
  private updatePlayerState(updates: Partial<EnhancedPlayerState>): void {
    const currentState = this.playerStateSubject.value;
    const newState = { ...currentState, ...updates, lastUpdated: new Date() };
    this.playerStateSubject.next(newState);
  }
}
