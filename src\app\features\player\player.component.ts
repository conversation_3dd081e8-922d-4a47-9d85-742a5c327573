// player.component.ts
import { <PERSON><PERSON>nent, On<PERSON>nit, On<PERSON><PERSON>roy, HostListener, NgZone, PLATFORM_ID, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { isPlatformBrowser } from '@angular/common';
import { Router } from '@angular/router';
import { Subscription, interval, fromEvent } from 'rxjs';
import { PlaybackService } from '../../core/services/playback.service';
import { ScheduleService } from '../../core/services/schedule.service';
import { HeartbeatService } from '../../core/services/heartbeat.service';
import { LogService } from '../../core/services/log.service';
import { SupabaseApiService } from '../../core/services/supabase-api.service';
import { WakeLockService } from '../../core/services/wake-lock.service';
import { Playlist } from '../../core/models/playlist.model';
import { CarouselPlayerComponent, CarouselState, TransitionType } from './components/carousel-player.component';

@Component({
  selector: 'app-player',
  standalone: true,
  imports: [
    CommonModule,
    CarouselPlayerComponent
  ],
  templateUrl: './player.component.html',
  styleUrls: ['./player.component.scss']
})
export class PlayerComponent implements OnInit, OnDestroy {
  // Carousel-related properties
  currentPlaylist: Playlist | null = null;
  carouselState: CarouselState | null = null;
  playbackError: string | null = null;
  transitionType: TransitionType = 'slide';

  // Device and system properties
  isFullscreen = false;
  isOnline = typeof window !== 'undefined' ? navigator.onLine : false;
  deviceValidationError: boolean = false;
  private isBrowser: boolean;

  // Timers and intervals
  private scheduleCheckInterval: Subscription | null = null;
  private heartbeatInterval: Subscription | null = null;
  private forceReloadTimeoutId: any = null;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private playbackService: PlaybackService,
    private scheduleService: ScheduleService,
    private heartbeatService: HeartbeatService,
    private logService: LogService,
    private supabaseApi: SupabaseApiService,
    private wakeLockService: WakeLockService,
    private router: Router,
    private zone: NgZone,
    @Inject(PLATFORM_ID) private platformId: any
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.logService.setDebugLevel(0);
    this.logService.info('Carousel Player component initialized');
  }

  ngOnInit(): void {
    // First validate device exists in database
    this.validateDevice().then(isValid => {
      if (!isValid) {
        this.deviceValidationError = true;
        this.playbackError = 'Device not found in system';
        this.logService.error('Device validation failed - device not found in database');
        return;
      }
      
      // Device is valid, proceed with normal initialization
      this.setupNetworkListeners();
      this.setupTimeChangeListeners();
      this.setupPlayback();
      this.setupScheduleChecking();
      this.setupPeriodicDeviceValidation();
      this.startHeartbeat();
      
      // Ensure we're in fullscreen mode
      this.enterFullscreen();
      
      // Request wake lock to keep screen always on
      this.requestWakeLock();
      
      // Log player startup
      this.logService.info('Player started');
      
      // Subscribe to schedule changes directly
      const scheduleChangeSub = this.scheduleService.scheduleChange$.subscribe(playlistId => {
        this.logService.info(`Schedule change received in player component: ${playlistId}`);
        // Force a complete reload to ensure the view updates
        this.forceReloadPlaylist(playlistId);
      });
      
      this.subscriptions.push(scheduleChangeSub);
    });
  }

  ngOnDestroy(): void {
    // Clear timeouts
    if (this.forceReloadTimeoutId) {
      clearTimeout(this.forceReloadTimeoutId);
    }

    // Release wake lock
    this.wakeLockService.releaseWakeLock();

    // Clean up all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
    if (this.heartbeatInterval) {
      this.heartbeatInterval.unsubscribe();
    }
    if (this.scheduleCheckInterval) {
      this.scheduleCheckInterval.unsubscribe();
    }

    this.logService.info('Carousel Player stopped');
  }
  
  // Handle key presses (useful for debugging/admin functions)
  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    // Diagnostic key shortcuts (only active in development)
    if (event.altKey && event.key === 'd') {
      this.router.navigate(['/diagnostics']);
    }
    
    // Toggle fullscreen with F key
    if (event.key === 'f') {
      this.toggleFullscreen();
    }
    
    // Exit fullscreen with Escape key
    if (event.key === 'Escape') {
      this.exitFullscreen();
    }
    
    // Force reload the current playlist with R key
    if (event.key === 'r') {
      this.playbackService.reloadPlaylist();
    }
  }
  
  // Toggle fullscreen on any mouse click or touch
  @HostListener('document:click')
  onDocumentClick(): void {
    this.toggleFullscreen();
  }

  private setupNetworkListeners(): void {
    // Only setup network listeners in browser environment
    if (!this.isBrowser) return;
    
    // Listen for online/offline events
    const onlineSubscription = fromEvent(window, 'online').subscribe(() => {
      this.isOnline = true;
      this.logService.info('Network connection restored');
      
      // Force reload playlist when we come back online
      this.playbackService.reloadPlaylist();
    });
    
    const offlineSubscription = fromEvent(window, 'offline').subscribe(() => {
      this.isOnline = false;
      this.logService.warn('Network connection lost');
    });
    
    this.subscriptions.push(onlineSubscription, offlineSubscription);
  }

  private setupTimeChangeListeners(): void {
    // Only setup time change listeners in browser environment
    if (!this.isBrowser) return;

    // Check when visibility changes (tab becomes active)
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.logService.info('Tab became visible, checking schedule');
        this.checkScheduleAndReload();
      }
    });
  }

  private setupPlayback(): void {
    // Subscribe to playback errors
    const errorSub = this.playbackService.playbackError$.subscribe(error => {
      this.zone.run(() => {
        this.playbackError = error;
        if (error) {
          this.logService.error(`Carousel playback error: ${error}`);
        }
      });
    });

    // Subscribe to player state changes to get playlist updates
    const playerStateSub = this.playbackService.playerState$.subscribe(state => {
      this.zone.run(() => {
        if (state.currentPlaylistId) {
          const playlist = this.playbackService.getCurrentPlaylist();
          if (playlist && playlist.id === state.currentPlaylistId) {
            this.currentPlaylist = playlist;
            this.logService.info(`Loaded playlist for carousel: ${playlist.name} with ${playlist.items.length} items`);
          }
        }
      });
    });

    this.subscriptions.push(errorSub, playerStateSub);

    // Start playback
    this.playbackService.startPlayback();
  }

  private setupScheduleChecking(): void {
    // Initial check on startup with a small delay to ensure everything is loaded
    setTimeout(() => {
      this.logService.info('Performing initial schedule check');
      this.checkScheduleAndReload();
    }, 3000);

    // Check every minute for schedule changes
    this.scheduleCheckInterval = interval(60000).subscribe(() => {
      this.logService.debug('Checking for schedule changes');
      this.checkScheduleAndReload();
    });
  }

  // Helper method to check schedule and reload if needed
  // Helper method to check schedule and reload if needed
  private checkScheduleAndReload(): void {
    this.scheduleService.checkSchedule().subscribe(
      (changed: boolean) => {
        if (changed) {
          this.logService.info('Schedule changed, reloading playlist');
          // Note: We no longer need to call reloadPlaylist directly here
          // because we're already reacting to the scheduleChange$ events
        } else {
          this.logService.debug('No schedule changes detected');
        }
      },
      (error: any) => {
        this.logService.error(`Schedule check error: ${error}`);
      }
    );
  }







  // Toggle fullscreen
  toggleFullscreen(): void {
    if (this.isFullscreen) {
      this.exitFullscreen();
    } else {
      this.enterFullscreen();
    }
  }

  // Enhanced fullscreen method with better cross-browser support
  enterFullscreen(): void {
    const elem = document.documentElement;
    
    try {
      this.logService.info('Attempting to enter fullscreen mode');
      
      if (elem.requestFullscreen) {
        elem.requestFullscreen();
      } else if ((elem as any).mozRequestFullScreen) { // Firefox
        (elem as any).mozRequestFullScreen();
      } else if ((elem as any).webkitRequestFullscreen) { // Chrome, Safari and Opera
        (elem as any).webkitRequestFullscreen();
      } else if ((elem as any).msRequestFullscreen) { // IE/Edge
        (elem as any).msRequestFullscreen();
      }
      
      this.isFullscreen = true;
      this.logService.info('Entered fullscreen mode');
    } catch (error) {
      this.logService.warn(`Could not enter fullscreen mode: ${error}`);
    }
  }

  // Method to exit fullscreen
  exitFullscreen(): void {
    try {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).mozCancelFullScreen) { // Firefox
        (document as any).mozCancelFullScreen();
      } else if ((document as any).webkitExitFullscreen) { // Chrome, Safari and Opera
        (document as any).webkitExitFullscreen();
      } else if ((document as any).msExitFullscreen) { // IE/Edge
        (document as any).msExitFullscreen();
      }
      
      this.isFullscreen = false;
      this.logService.info('Exited fullscreen mode');
    } catch (error) {
      this.logService.warn(`Could not exit fullscreen mode: ${error}`);
    }
  }

  // Track fullscreen state changes
  @HostListener('document:fullscreenchange', ['$event'])
  @HostListener('document:webkitfullscreenchange', ['$event'])
  @HostListener('document:mozfullscreenchange', ['$event'])
  @HostListener('document:MSFullscreenChange', ['$event'])
  onFullscreenChange(): void {
    // Update the fullscreen state based on the document's fullscreen status
    this.isFullscreen = !!document.fullscreenElement || 
                       !!(document as any).webkitFullscreenElement || 
                       !!(document as any).mozFullScreenElement || 
                       !!(document as any).msFullscreenElement;
    
    this.logService.info(`Fullscreen state changed: ${this.isFullscreen ? 'enabled' : 'disabled'}`);
  }

  // Force a reload of a playlist
  private forceReloadPlaylist(playlistId: string): void {
    // Cancel any existing reload
    if (this.forceReloadTimeoutId) {
      clearTimeout(this.forceReloadTimeoutId);
    }

    // Allow time for the reset to take effect in the UI
    this.forceReloadTimeoutId = setTimeout(() => {
      this.zone.run(() => {
        this.logService.info(`Force reloading playlist: ${playlistId}`);
        this.playbackService.loadPlaylist(playlistId);
      });
    }, 300);
  }

  private async requestWakeLock(): Promise<void> {
    this.logService.info('🔥 INITIATING NEVER-SLEEP MODE FOR DIGITAL SIGNAGE! 🔥');
    
    const success = await this.wakeLockService.requestWakeLock();
    if (success) {
      this.logService.info('🚀 AGGRESSIVE WAKE LOCK ACTIVATED - SCREEN WILL NEVER SLEEP! 🚀');
      
      // Set up monitoring to ensure wake lock stays active
      this.setupWakeLockMonitoring();
    } else {
      this.logService.error('❌ CRITICAL: Failed to activate keep-alive methods - screen may sleep!');
      
      // Try to reactivate after a short delay
      setTimeout(() => {
        this.requestWakeLock();
      }, 5000);
    }
  }

  private setupWakeLockMonitoring(): void {
    // Monitor wake lock status every 30 seconds
    const monitorInterval = setInterval(() => {
      const status = this.wakeLockService.getWakeLockStatus();
      
      if (!status.active) {
        this.logService.warn('⚠️ ALERT: Wake lock is not active - reactivating immediately!');
        this.wakeLockService.forceReactivate();
      } else {
        this.logService.debug(`✅ Wake lock monitoring: ${status.methods.length} methods active`);
      }
    }, 30000);

    // Store the interval so we can clear it on destroy
    this.subscriptions.push({
      unsubscribe: () => clearInterval(monitorInterval)
    } as any);
  }

  private startHeartbeat(): void {
    // Send heartbeat immediately on startup
    this.sendHeartbeat();
    
    // Send heartbeat every 60 seconds
    this.heartbeatInterval = interval(60000).subscribe(() => {
      this.sendHeartbeat();
    });
  }
  
  private sendHeartbeat(): void {
    const wakeLockStatus = this.wakeLockService.getWakeLockStatus();

    this.heartbeatService.sendHeartbeat({
      status: this.playbackError ? 'error' : this.carouselState?.isPlaying ? 'playing' : 'paused',
      currentItem: null, // Carousel handles items internally
      currentPlaylist: this.currentPlaylist?.id || null,
      error: this.playbackError,
      wakeLockActive: wakeLockStatus.active,
      wakeLockSupported: wakeLockStatus.supported,
      wakeLockMethods: wakeLockStatus.methods.join(', ')
    }).subscribe(
      success => {
        if (success) {
          this.logService.debug(`Heartbeat sent - Wake lock: ${wakeLockStatus.active ? 'ACTIVE' : 'INACTIVE'} (${wakeLockStatus.methods.length} methods)`);
        } else {
          this.logService.warn('Heartbeat failed');
        }
      },
      error => {
        this.logService.error(`Heartbeat error: ${error}`);
      }
    );
  }
  


  // Carousel event handlers
  onCarouselStateChange(state: CarouselState): void {
    this.carouselState = state;
    this.logService.debug(`Carousel state changed: ${state.isPlaying ? 'playing' : 'paused'} - Item ${state.currentIndex + 1}/${state.totalItems}`);
  }

  onPlaylistEnded(): void {
    this.logService.info('Playlist ended - checking for schedule updates');
    this.checkScheduleAndReload();
  }

  onCarouselError(error: string): void {
    this.playbackError = error;
    this.logService.error(`Carousel error: ${error}`);

    // Auto-retry after 5 seconds
    setTimeout(() => {
      this.playbackError = null;
      this.checkScheduleAndReload();
    }, 5000);
  }


  
  // Force reload the current playlist
  reloadPlaylist(): void {
    if (this.currentPlaylist?.id) {
      this.forceReloadPlaylist(this.currentPlaylist.id);
    } else {
      this.playbackService.reloadPlaylist();
    }
  }

  // Reset device registration and return to registration screen
  resetDevice(): void {
    this.logService.info('Resetting device registration');
    
    // Clear all device registration data from localStorage
    if (this.isBrowser) {
      localStorage.removeItem('deviceId');
      localStorage.removeItem('registrationCode');
      localStorage.removeItem('deviceName');
      localStorage.removeItem('registrationData');
    }
    
    // Stop all services
    this.ngOnDestroy();
    
    // Navigate to registration screen
    this.router.navigate(['/registration']);
  }

  // Validate that the device still exists in the database
  private async validateDevice(): Promise<boolean> {
    if (!this.isBrowser) {
      return true; // Skip validation during SSR
    }

    const deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      this.logService.warn('No device ID found in localStorage');
      return false;
    }

    try {
      const screen = await new Promise((resolve, reject) => {
        this.supabaseApi.getScreenById(deviceId).subscribe({
          next: resolve,
          error: reject
        });
      });

      if (!screen) {
        this.logService.error(`Device ${deviceId} not found in database`);
        return false;
      }

      this.logService.info(`Device ${deviceId} validated successfully`);
      return true;
    } catch (error) {
      this.logService.error(`Error validating device: ${error}`);
      return false;
    }
  }

  // Setup periodic device validation checks
  private setupPeriodicDeviceValidation(): void {
    // Check device validity every 5 minutes
    const validationInterval = setInterval(async () => {
      const isValid = await this.validateDevice();
      if (!isValid) {
        this.logService.warn('Device validation failed during periodic check');
        this.zone.run(() => {
          this.deviceValidationError = true;
          this.playbackError = 'Device was removed from system';
        });
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Store the interval so we can clear it on destroy
    const subscription = new Subscription();
    subscription.add(() => clearInterval(validationInterval));
    this.subscriptions.push(subscription);
  }
}