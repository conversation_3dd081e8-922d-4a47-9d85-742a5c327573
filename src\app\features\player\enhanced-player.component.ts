// enhanced-player.component.ts
import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener, Inject, PLATFORM_ID, ElementRef, NgZone } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Router } from '@angular/router';
import { Observable, Subscription, interval } from 'rxjs';
import { EnhancedPlaybackService, EnhancedPlayerState } from '../../core/services/enhanced-playback.service';
import { ScheduleService } from '../../core/services/schedule.service';
import { HeartbeatService } from '../../core/services/heartbeat.service';
import { LogService } from '../../core/services/log.service';
import { SupabaseApiService } from '../../core/services/supabase-api.service';
import { WakeLockService } from '../../core/services/wake-lock.service';
import { MediaManagementService, MediaDownloadProgress } from '../../core/services/media-management.service';
import { Playlist, PlaylistItem } from '../../core/models/playlist.model';
import { CarouselPlayerComponent, CarouselState, TransitionType } from './components/carousel-player.component';

@Component({
  selector: 'app-enhanced-player',
  standalone: true,
  imports: [
    CommonModule,
    CarouselPlayerComponent
  ],
  templateUrl: './enhanced-player.component.html',
  styleUrls: ['./enhanced-player.component.scss']
})
export class EnhancedPlayerComponent implements OnInit, OnDestroy {
  // Component state
  currentPlaylist: Playlist | null = null;
  currentPlayerState: EnhancedPlayerState | null = null;
  carouselState: CarouselState | null = null;
  playbackError: string | null = null;
  downloadProgress: MediaDownloadProgress | null = null;
  
  // UI state
  isFullscreen = false;
  isOnline = typeof window !== 'undefined' ? navigator.onLine : false;
  deviceValidationError = false;
  showDiagnostics = false;
  showProgress = true;
  transitionType: TransitionType = 'slide';
  
  // Browser detection
  private isBrowser: boolean;
  
  // Subscriptions and timers
  private subscriptions: Subscription[] = [];
  private heartbeatInterval: Subscription | null = null;
  private scheduleCheckInterval: Subscription | null = null;
  private diagnosticsTimeout: any = null;

  constructor(
    private enhancedPlaybackService: EnhancedPlaybackService,
    private scheduleService: ScheduleService,
    private heartbeatService: HeartbeatService,
    private logService: LogService,
    private supabaseApi: SupabaseApiService,
    private wakeLockService: WakeLockService,
    private mediaManagementService: MediaManagementService,
    private router: Router,
    private elementRef: ElementRef,
    private zone: NgZone,
    @Inject(PLATFORM_ID) private platformId: any
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.logService.setDebugLevel(0);
    this.logService.info('Enhanced Player component initialized');
  }

  ngOnInit(): void {
    if (!this.isBrowser) return;

    // Validate device first
    this.validateDevice().then(isValid => {
      if (!isValid) {
        this.deviceValidationError = true;
        return;
      }

      // Device is valid, proceed with initialization
      this.setupNetworkListeners();
      this.setupPlayback();
      this.setupScheduleChecking();
      this.setupPeriodicDeviceValidation();
      this.startHeartbeat();
      
      // UI setup
      this.enterFullscreen();
      this.requestWakeLock();
      
      this.logService.info('Enhanced Player started');
    });
  }

  ngOnDestroy(): void {
    this.cleanup();
  }

  // Keyboard event handlers
  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (event.altKey && event.key === 'd') {
      this.router.navigate(['/diagnostics']);
    }
    
    if (event.key === 'f') {
      this.toggleFullscreen();
    }
    
    if (event.key === 'Escape') {
      this.exitFullscreen();
    }
    
    if (event.key === 'r') {
      this.reloadPlaylist();
    }

    if (event.key === 'i') {
      this.toggleDiagnostics();
    }

    // Carousel controls
    if (event.key === 'ArrowRight') {
      this.skipToNext();
    }

    if (event.key === 'ArrowLeft') {
      this.skipToPrevious();
    }

    if (event.key === ' ') {
      event.preventDefault();
      this.togglePlayback();
    }
  }

  @HostListener('document:click')
  onDocumentClick(): void {
    this.toggleFullscreen();
  }

  @HostListener('window:online')
  onOnline(): void {
    this.zone.run(() => {
      this.isOnline = true;
      this.logService.info('Network connection restored');
    });
  }

  @HostListener('window:offline')
  onOffline(): void {
    this.zone.run(() => {
      this.isOnline = false;
      this.logService.info('Network connection lost - switching to offline mode');
    });
  }

  // Carousel event handlers
  onCarouselStateChange(state: CarouselState): void {
    this.carouselState = state;
  }

  onPlaylistEnded(): void {
    this.logService.info('Playlist ended - checking for schedule updates');
    this.checkScheduleAndLoadPlaylist();
  }

  onCarouselError(error: string): void {
    this.playbackError = error;
    this.logService.error(`Carousel error: ${error}`);
    
    // Auto-retry after 5 seconds
    setTimeout(() => {
      this.retryPlayback();
    }, 5000);
  }

  // Control methods
  skipToNext(): void {
    // This would be handled by the carousel component
    this.logService.info('Skip to next requested');
  }

  skipToPrevious(): void {
    // This would be handled by the carousel component
    this.logService.info('Skip to previous requested');
  }

  togglePlayback(): void {
    if (this.carouselState?.isPlaying) {
      this.enhancedPlaybackService.pausePlayback();
    } else {
      this.enhancedPlaybackService.resumePlayback();
    }
  }

  reloadPlaylist(): void {
    this.enhancedPlaybackService.reloadPlaylist();
  }

  retryPlayback(): void {
    this.playbackError = null;
    this.enhancedPlaybackService.clearError();
    this.checkScheduleAndLoadPlaylist();
  }

  reloadPage(): void {
    if (this.isBrowser) {
      window.location.reload();
    }
  }

  toggleDiagnostics(): void {
    this.showDiagnostics = !this.showDiagnostics;
    
    if (this.showDiagnostics) {
      // Auto-hide after 10 seconds
      this.diagnosticsTimeout = setTimeout(() => {
        this.showDiagnostics = false;
      }, 10000);
    } else if (this.diagnosticsTimeout) {
      clearTimeout(this.diagnosticsTimeout);
      this.diagnosticsTimeout = null;
    }
  }

  // Private setup methods
  private async validateDevice(): Promise<boolean> {
    try {
      const deviceId = this.getDeviceId();
      if (!deviceId) {
        this.logService.error('No device ID found');
        return false;
      }

      const screen = await this.supabaseApi.getScreenById(deviceId).toPromise();
      return !!screen;
    } catch (error) {
      this.logService.error('Device validation failed:', error);
      return false;
    }
  }

  private setupNetworkListeners(): void {
    if (!this.isBrowser) return;

    // Initial network state
    this.isOnline = navigator.onLine;
  }

  private setupPlayback(): void {
    // Subscribe to player state changes
    const playerStateSub = this.enhancedPlaybackService.playerState$.subscribe(state => {
      this.zone.run(() => {
        this.currentPlayerState = state;
        this.downloadProgress = state.downloadProgress || null;
      });
    });

    // Subscribe to current playlist changes - we'll get this from the service when loaded

    // Subscribe to playback errors
    const errorSub = this.enhancedPlaybackService.playbackError$.subscribe(error => {
      this.zone.run(() => {
        this.playbackError = error;
      });
    });

    this.subscriptions.push(playerStateSub, errorSub);

    // Start initial playlist loading
    this.checkScheduleAndLoadPlaylist();
  }

  private setupScheduleChecking(): void {
    // Check schedule every minute
    this.scheduleCheckInterval = interval(60000).subscribe(() => {
      this.checkScheduleAndLoadPlaylist();
    });

    // Subscribe to schedule changes
    const scheduleChangeSub = this.scheduleService.scheduleChange$.subscribe(playlistId => {
      this.logService.info(`Schedule change received: ${playlistId}`);
      if (playlistId) {
        this.loadPlaylist(playlistId);
      }
    });

    this.subscriptions.push(scheduleChangeSub);
  }

  private setupPeriodicDeviceValidation(): void {
    // Validate device every 30 minutes
    interval(30 * 60 * 1000).subscribe(() => {
      this.validateDevice().then(isValid => {
        if (!isValid) {
          this.deviceValidationError = true;
        }
      });
    });
  }

  private startHeartbeat(): void {
    // Send heartbeat immediately on startup
    this.sendHeartbeat();

    // Send heartbeat every 60 seconds
    this.heartbeatInterval = interval(60000).subscribe(() => {
      this.sendHeartbeat();
    });
  }

  private sendHeartbeat(): void {
    const wakeLockStatus = this.wakeLockService.getWakeLockStatus();

    this.heartbeatService.sendHeartbeat({
      status: this.playbackError ? 'error' : this.carouselState?.isPlaying ? 'playing' : 'paused',
      currentItem: this.carouselState?.currentItem?.id,
      currentPlaylist: this.currentPlayerState?.currentPlaylistId || null,
      error: this.playbackError,
      wakeLockActive: wakeLockStatus.active,
      wakeLockSupported: wakeLockStatus.supported,
      wakeLockMethods: wakeLockStatus.methods.join(', ')
    }).subscribe({
      next: (success) => {
        if (success) {
          this.logService.debug(`Heartbeat sent - Wake lock: ${wakeLockStatus.active ? 'ACTIVE' : 'INACTIVE'}`);
        } else {
          this.logService.warn('Heartbeat failed');
        }
      },
      error: (error) => {
        this.logService.error(`Heartbeat error: ${error}`);
      }
    });
  }

  private checkScheduleAndLoadPlaylist(): void {
    const deviceId = this.getDeviceId();
    if (!deviceId) {
      this.logService.error('No device ID for schedule check');
      return;
    }

    // Use the schedule service to check for current schedule
    this.scheduleService.checkSchedule().subscribe({
      next: (changed: boolean) => {
        if (changed) {
          this.logService.info('Schedule changed, reloading playlist');
          // The schedule service will emit the new playlist ID via scheduleChange$
        } else {
          this.logService.debug('No schedule changes detected');
        }
      },
      error: (error: any) => {
        this.logService.error('Failed to check schedule:', error);
      }
    });
  }

  private loadPlaylist(playlistId: string): void {
    this.logService.info(`Loading playlist: ${playlistId}`);

    this.enhancedPlaybackService.loadPlaylistWithDownload(playlistId).subscribe({
      next: (success) => {
        if (success) {
          // Get the current playlist from the service's internal state
          const currentState = this.enhancedPlaybackService.getCurrentState();
          if (currentState.currentPlaylistId === playlistId) {
            // We need to get the actual playlist object - let's use the regular playback service for now
            this.logService.info(`Playlist loaded successfully: ${playlistId}`);
          }
        } else {
          this.logService.error(`Failed to load playlist: ${playlistId}`);
        }
      },
      error: (error) => {
        this.logService.error(`Error loading playlist: ${error.message}`);
        this.playbackError = `Failed to load playlist: ${error.message}`;
      }
    });
  }

  // Fullscreen methods
  private enterFullscreen(): void {
    if (!this.isBrowser) return;

    const element = this.elementRef.nativeElement;
    if (element.requestFullscreen) {
      element.requestFullscreen().catch(() => {});
    }
    this.isFullscreen = true;
  }

  private exitFullscreen(): void {
    if (!this.isBrowser) return;

    if (document.exitFullscreen) {
      document.exitFullscreen().catch(() => {});
    }
    this.isFullscreen = false;
  }

  private toggleFullscreen(): void {
    if (this.isFullscreen) {
      this.exitFullscreen();
    } else {
      this.enterFullscreen();
    }
  }

  private requestWakeLock(): void {
    this.wakeLockService.requestWakeLock().then(success => {
      if (success) {
        this.logService.info('Wake lock acquired');
      }
    });
  }

  private getDeviceId(): string | null {
    if (!this.isBrowser) return null;
    return localStorage.getItem('deviceId');
  }

  private cleanup(): void {
    // Clear all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());

    // Clear intervals
    if (this.heartbeatInterval) {
      this.heartbeatInterval.unsubscribe();
    }

    if (this.scheduleCheckInterval) {
      this.scheduleCheckInterval.unsubscribe();
    }

    // Clear timeouts
    if (this.diagnosticsTimeout) {
      clearTimeout(this.diagnosticsTimeout);
    }

    // Release wake lock
    this.wakeLockService.releaseWakeLock();

    this.logService.info('Enhanced Player component destroyed');
  }
}
