/* enhanced-player.component.scss */
:host {
  display: block;
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  overflow: hidden;
  background-color: #000;
  position: fixed;
  top: 0;
  left: 0;
  margin: 0 !important;
  padding: 0 !important;
}

.enhanced-player-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #000;
}

/* Error overlay styles */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.error-container {
  text-align: center;
  padding: 3rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  max-width: 80%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  .friendly-icon {
    font-size: 5rem;
    color: #64b5f6;
    margin-bottom: 1.5rem;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  }
  
  h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: 300;
    color: #ffffff;
  }
  
  .friendly-message {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #e3f2fd;
  }
  
  .recovery-message {
    margin-top: 1rem;
    font-style: italic;
    opacity: 0.8;
    color: #bbdefb;
    font-size: 0.9rem;
  }
}

.reload-button {
  margin-top: 2rem;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  padding: 0.875rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  margin-left: auto;
  margin-right: auto;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  font-size: 0.95rem;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  }
  
  &:active {
    transform: translateY(0);
  }
}

/* Download progress overlay */
.download-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
  z-index: 90;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.download-container {
  text-align: center;
  padding: 3rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  max-width: 80%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 400px;

  h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    font-weight: 300;
  }

  p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
    font-size: 1rem;
  }
}

.download-spinner {
  width: 60px;
  height: 60px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin: 0 auto 1.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.download-progress-bar {
  width: 100%;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.download-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #fff 0%, #e8f5e8 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.download-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Offline banner */
.offline-banner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0.5rem 1rem;
  background-color: rgba(255, 193, 7, 0.9);
  color: #212529;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  z-index: 80;
  font-size: 0.875rem;
  font-weight: 500;
  
  .material-icons {
    font-size: 1rem;
  }
}

/* Diagnostics overlay */
.diagnostics-overlay {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  z-index: 70;
}

.info-pill {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.8rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  .status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    
    &.online {
      background-color: #10b981;
      box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
    }
    
    &.offline {
      background-color: #ef4444;
      box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
    }
  }
  
  .playlist-name {
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
  }
}

.control-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: opacity 0.2s, transform 0.2s;
  
  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  .material-icons {
    font-size: 1.2rem;
  }
}

.refresh-button {
  color: #10b981;
}

/* Fullscreen styles */
.enhanced-player-container.fullscreen {
  width: 100vw !important;
  height: 100vh !important;
}

:fullscreen .enhanced-player-container,
:-webkit-full-screen .enhanced-player-container,
:-moz-full-screen .enhanced-player-container,
:-ms-fullscreen .enhanced-player-container {
  width: 100vw;
  height: 100vh;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .download-container {
    min-width: 300px;
    padding: 2rem;
  }
  
  .error-container {
    padding: 2rem;
    
    .friendly-icon {
      font-size: 4rem;
    }
    
    h3 {
      font-size: 1.5rem;
    }
  }
  
  .info-pill {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    
    .playlist-name {
      max-width: 100px;
    }
  }
}

/* Smooth transitions */
* {
  box-sizing: border-box;
}

/* Ensure carousel fills the container */
app-carousel-player {
  display: block;
  width: 100%;
  height: 100%;
}
