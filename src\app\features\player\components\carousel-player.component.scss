/* carousel-player.component.scss */
:host {
  display: block;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #000;

  /* Smooth hardware acceleration for better performance */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  /* Hardware acceleration for smooth transitions */
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform, opacity;

  /* Ensure smooth rendering during transitions */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Prevent any flickering during transitions */
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

.current-slide {
  z-index: 10;
}

.next-slide {
  z-index: 5;
}

/* Fade transition animations */
.fade-out {
  animation: fadeOut 0.5s ease-in-out forwards;
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Slide transition animations - Smooth carousel style */
.slide-out-left {
  animation: slideOutLeft var(--transition-duration, 0.8s) cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  box-shadow: -10px 0 20px rgba(0, 0, 0, 0.3);
}

.slide-in-right {
  animation: slideInRight var(--transition-duration, 0.8s) cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  box-shadow: 10px 0 20px rgba(0, 0, 0, 0.3);
}

.slide-out-right {
  animation: slideOutRight var(--transition-duration, 0.8s) cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  box-shadow: 10px 0 20px rgba(0, 0, 0, 0.3);
}

.slide-in-left {
  animation: slideInLeft var(--transition-duration, 0.8s) cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  box-shadow: -10px 0 20px rgba(0, 0, 0, 0.3);
}

@keyframes slideOutLeft {
  0% {
    transform: translateX(0) translateZ(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%) translateZ(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%) translateZ(0);
    opacity: 1;
  }
  100% {
    transform: translateX(0) translateZ(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  0% {
    transform: translateX(0) translateZ(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%) translateZ(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  0% {
    transform: translateX(-100%) translateZ(0);
    opacity: 1;
  }
  100% {
    transform: translateX(0) translateZ(0);
    opacity: 1;
  }
}

/* Loading overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 50;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error overlay */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  color: white;
}

.error-content {
  text-align: center;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 80%;

  .material-icons {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.9;
  }

  h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 300;
  }

  p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
  }
}

.retry-button {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  padding: 0.75rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  margin: 0 auto;

  &:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
  }

  .material-icons {
    font-size: 1.2rem;
  }
}

/* Progress indicator */
.progress-indicator {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 20;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 25px;
  padding: 10px 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 200px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #8BC34A 100%);
  border-radius: 2px;
  transition: width 0.1s ease-out;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Transition states */
.transitioning {
  .progress-indicator {
    opacity: 0.5;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .progress-indicator {
    bottom: 10px;
    min-width: 150px;
    padding: 8px 15px;
  }

  .progress-info {
    font-size: 0.7rem;
  }

  .error-content {
    padding: 1.5rem;
    
    .material-icons {
      font-size: 3rem;
    }
    
    h3 {
      font-size: 1.25rem;
    }
  }
}

/* Hide progress when not needed */
.carousel-container:not(.show-progress) .progress-indicator {
  display: none;
}

/* Smooth transitions for all elements */
* {
  box-sizing: border-box;
}

/* Ensure media components fill the slide */
.carousel-slide ::ng-deep {
  app-image-item,
  app-video-item,
  app-web-item {
    width: 100%;
    height: 100%;
    display: block;
  }
}
