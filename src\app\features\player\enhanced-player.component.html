<!-- enhanced-player.component.html -->
<div class="enhanced-player-container" [class.fullscreen]="isFullscreen">
  <!-- Device validation error -->
  <div *ngIf="deviceValidationError" class="error-overlay">
    <div class="error-container">
      <span class="material-icons friendly-icon">devices</span>
      <h3>Device Setup Required</h3>
      <p class="friendly-message">This device needs to be registered before it can display content.</p>
      <p class="recovery-message">Please contact your administrator to register this device.</p>
      <button class="reload-button" (click)="reloadPage()">
        <span class="material-icons">refresh</span>
        Retry
      </button>
    </div>
  </div>

  <!-- Offline banner -->
  <div *ngIf="!isOnline && !deviceValidationError" class="offline-banner">
    <span class="material-icons">wifi_off</span>
    <span>Offline Mode - Using cached content</span>
  </div>

  <!-- Download progress overlay -->
  <div *ngIf="downloadProgress && !downloadProgress.isComplete" class="download-overlay">
    <div class="download-container">
      <div class="download-spinner"></div>
      <h3>Preparing Content</h3>
      <p>{{ downloadProgress.currentItem || 'Downloading media files...' }}</p>
      <div class="download-progress-bar">
        <div class="download-progress-fill" [style.width.%]="downloadProgress.progress"></div>
      </div>
      <div class="download-info">
        <span>{{ downloadProgress.downloadedItems }} / {{ downloadProgress.totalItems }}</span>
        <span>{{ downloadProgress.progress }}%</span>
      </div>
    </div>
  </div>

  <!-- Main carousel player -->
  <app-carousel-player
    *ngIf="currentPlaylist && !deviceValidationError"
    [playlist]="currentPlaylist"
    [autoPlay]="true"
    [loop]="true"
    [transitionType]="transitionType"
    [transitionDuration]="800"
    [showProgress]="showProgress"
    (stateChange)="onCarouselStateChange($event)"
    (playlistEnded)="onPlaylistEnded()"
    (error)="onCarouselError($event)">
  </app-carousel-player>

  <!-- Error overlay for playback errors -->
  <div *ngIf="playbackError && !deviceValidationError" class="error-overlay">
    <div class="error-container">
      <span class="material-icons friendly-icon">error_outline</span>
      <h3>Playback Error</h3>
      <p class="friendly-message">{{ playbackError }}</p>
      <p class="recovery-message">Attempting to recover automatically...</p>
      <button class="reload-button" (click)="retryPlayback()">
        <span class="material-icons">refresh</span>
        Retry Now
      </button>
    </div>
  </div>

  <!-- Diagnostics overlay -->
  <div class="diagnostics-overlay" *ngIf="showDiagnostics">
    <div class="info-pill">
      <div class="status-indicator" [class.online]="isOnline" [class.offline]="!isOnline"></div>
      <span class="playlist-name">{{ currentPlayerState?.currentPlaylistName || 'No playlist' }}</span>
      <span>{{ (currentPlayerState?.currentItemIndex || 0) + 1 }}/{{ currentPlayerState?.totalItems || 0 }}</span>
      <button class="control-button refresh-button" (click)="reloadPlaylist()" title="Reload playlist">
        <span class="material-icons">refresh</span>
      </button>
    </div>
  </div>
</div>
