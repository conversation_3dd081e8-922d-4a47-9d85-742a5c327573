// carousel-player.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ChangeDetector<PERSON>ef, NgZone } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable, Subscription, timer, BehaviorSubject } from 'rxjs';
import { Playlist, PlaylistItem } from '../../../core/models/playlist.model';
import { LogService } from '../../../core/services/log.service';
import { ContentSyncService } from '../../../core/services/content-sync.service';
import { TimedPlaybackService, TimerConfig } from '../../../core/services/timed-playback.service';
import { ImageItemComponent } from './image-item.component';
import { VideoItemComponent } from './video-item.component';
import { WebItemComponent } from './web-item.component';

export interface CarouselState {
  currentIndex: number;
  totalItems: number;
  isPlaying: boolean;
  isPaused: boolean;
  currentItem: PlaylistItem | null;
  nextItem: PlaylistItem | null;
  progress: number; // 0-100 for current item
  timeRemaining: number; // seconds
}

export type TransitionType = 'fade' | 'slide' | 'none';
export type SlideDirection = 'left' | 'right';

@Component({
  selector: 'app-carousel-player',
  standalone: true,
  imports: [CommonModule, ImageItemComponent, VideoItemComponent, WebItemComponent],
  template: `
    <div class="carousel-container"
         [class.transitioning]="isTransitioning"
         [style.--transition-duration]="transitionDuration + 'ms'">
      <!-- Current slide -->
      <div
        class="carousel-slide current-slide"
        [class.fade-out]="isTransitioning && transitionType === 'fade'"
        [class.slide-out-left]="isTransitioning && transitionType === 'slide' && slideDirection === 'left'"
        [class.slide-out-right]="isTransitioning && transitionType === 'slide' && slideDirection === 'right'"
        *ngIf="currentItem">
        <ng-container [ngSwitch]="currentItem.type">
          <app-image-item
            *ngSwitchCase="'image'"
            [item]="currentItem"
            [scaling]="'stretch'"
            [preload]="false"
            (ended)="onItemEnded()">
          </app-image-item>

          <app-video-item
            *ngSwitchCase="'video'"
            [item]="currentItem"
            [scaling]="'stretch'"
            [muted]="currentItem?.settings?.muted ?? true"
            [loop]="false"
            [preload]="false"
            (ended)="onItemEnded()">
          </app-video-item>

          <app-web-item
            *ngSwitchCase="'webpage'"
            [item]="currentItem"
            [duration]="currentItem?.duration || 10"
            [preload]="false"
            (ended)="onItemEnded()">
          </app-web-item>
        </ng-container>
      </div>

      <!-- Next slide (for transitions) -->
      <div
        class="carousel-slide next-slide"
        [class.fade-in]="isTransitioning && transitionType === 'fade'"
        [class.slide-in-right]="isTransitioning && transitionType === 'slide' && slideDirection === 'left'"
        [class.slide-in-left]="isTransitioning && transitionType === 'slide' && slideDirection === 'right'"
        *ngIf="nextItem && isTransitioning">
        <ng-container [ngSwitch]="nextItem.type">
          <app-image-item
            *ngSwitchCase="'image'"
            [item]="nextItem"
            [scaling]="'stretch'"
            [preload]="true">
          </app-image-item>

          <app-video-item
            *ngSwitchCase="'video'"
            [item]="nextItem"
            [scaling]="'stretch'"
            [muted]="nextItem?.settings?.muted ?? true"
            [loop]="false"
            [preload]="true">
          </app-video-item>

          <app-web-item
            *ngSwitchCase="'webpage'"
            [item]="nextItem"
            [duration]="nextItem?.duration || 10"
            [preload]="true">
          </app-web-item>
        </ng-container>
      </div>

      <!-- Loading overlay -->
      <div class="loading-overlay" *ngIf="isLoading">
        <div class="loading-spinner"></div>
        <p>Loading content...</p>
      </div>

      <!-- Error overlay -->
      <div class="error-overlay" *ngIf="hasError">
        <div class="error-content">
          <span class="material-icons">error_outline</span>
          <h3>Content Error</h3>
          <p>{{ errorMessage }}</p>
          <button class="retry-button" (click)="retryCurrentItem()">
            <span class="material-icons">refresh</span>
            Retry
          </button>
        </div>
      </div>

      <!-- Progress indicator -->
      <div class="progress-indicator" *ngIf="showProgress && !hasError">
        <div class="progress-bar">
          <div class="progress-fill" [style.width.%]="carouselState.progress"></div>
        </div>
        <div class="progress-info">
          <span>{{ carouselState.currentIndex + 1 }} / {{ carouselState.totalItems }}</span>
          <span>{{ formatTime(carouselState.timeRemaining) }}</span>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./carousel-player.component.scss']
})
export class CarouselPlayerComponent implements OnInit, OnDestroy {
  @Input() playlist: Playlist | null = null;
  @Input() autoPlay = true;
  @Input() loop = true;
  @Input() transitionType: TransitionType = 'fade';
  @Input() transitionDuration = 500; // milliseconds
  @Input() showProgress = true;
  @Output() stateChange = new EventEmitter<CarouselState>();
  @Output() playlistEnded = new EventEmitter<void>();
  @Output() error = new EventEmitter<string>();

  // Component state
  currentItem: PlaylistItem | null = null;
  nextItem: PlaylistItem | null = null;
  isTransitioning = false;
  isLoading = false;
  hasError = false;
  errorMessage = '';
  slideDirection: SlideDirection = 'left';

  // Carousel state
  private carouselStateSubject = new BehaviorSubject<CarouselState>({
    currentIndex: 0,
    totalItems: 0,
    isPlaying: false,
    isPaused: false,
    currentItem: null,
    nextItem: null,
    progress: 0,
    timeRemaining: 0
  });

  carouselState = this.carouselStateSubject.value;

  // Timers and subscriptions
  private itemTimer: any = null;
  private progressTimer: any = null;
  private transitionTimer: any = null;
  private subscriptions: Subscription[] = [];

  constructor(
    private logService: LogService,
    private contentSyncService: ContentSyncService,
    private timedPlaybackService: TimedPlaybackService,
    private cdr: ChangeDetectorRef,
    private zone: NgZone
  ) {
    // Subscribe to state changes
    this.subscriptions.push(
      this.carouselStateSubject.subscribe(state => {
        this.carouselState = state;
        this.stateChange.emit(state);
      })
    );

    // Subscribe to timer completion
    this.subscriptions.push(
      this.timedPlaybackService.getTimerCompleted().subscribe(item => {
        this.onItemEnded();
      })
    );

    // Subscribe to timer progress
    this.subscriptions.push(
      this.timedPlaybackService.getTimerProgress().subscribe(({ progress, remaining }) => {
        this.updateState({ progress, timeRemaining: remaining });
      })
    );
  }

  ngOnInit(): void {
    if (this.playlist && this.autoPlay) {
      this.startPlayback();
    }
  }

  ngOnDestroy(): void {
    this.stopAllTimers();
    this.timedPlaybackService.stopTimer();
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Start or restart playback
   */
  startPlayback(): void {
    if (!this.playlist || !this.playlist.items || this.playlist.items.length === 0) {
      this.handleError('No playlist or playlist is empty');
      return;
    }

    this.logService.info(`Starting carousel playback: ${this.playlist.name}`);
    this.hasError = false;
    this.isLoading = true;
    
    this.updateState({
      currentIndex: 0,
      totalItems: this.playlist.items.length,
      isPlaying: true,
      isPaused: false
    });

    this.loadAndPlayItem(0);
  }

  /**
   * Pause playback
   */
  pause(): void {
    this.timedPlaybackService.pauseTimer();
    this.updateState({ isPaused: true, isPlaying: false });
    this.logService.info('Carousel playback paused');
  }

  /**
   * Resume playback
   */
  resume(): void {
    if (this.carouselState.isPaused) {
      this.timedPlaybackService.resumeTimer();
      this.updateState({ isPaused: false, isPlaying: true });
      this.logService.info('Carousel playback resumed');
    }
  }

  /**
   * Skip to next item
   */
  next(): void {
    if (!this.playlist || this.playlist.items.length === 0 || this.isTransitioning) return;

    const nextIndex = (this.carouselState.currentIndex + 1) % this.playlist.items.length;
    this.slideDirection = 'left'; // Moving forward = slide left
    this.transitionToItem(nextIndex);
  }

  /**
   * Skip to previous item
   */
  previous(): void {
    if (!this.playlist || this.playlist.items.length === 0 || this.isTransitioning) return;

    const prevIndex = this.carouselState.currentIndex === 0
      ? this.playlist.items.length - 1
      : this.carouselState.currentIndex - 1;
    this.slideDirection = 'right'; // Moving backward = slide right
    this.transitionToItem(prevIndex);
  }

  /**
   * Jump to specific item
   */
  goToItem(index: number): void {
    if (!this.playlist || index < 0 || index >= this.playlist.items.length || this.isTransitioning) return;

    // Determine slide direction based on target index
    const currentIndex = this.carouselState.currentIndex;
    if (index > currentIndex) {
      this.slideDirection = 'left'; // Moving forward
    } else if (index < currentIndex) {
      this.slideDirection = 'right'; // Moving backward
    } else {
      return; // Same item, no transition needed
    }

    this.transitionToItem(index);
  }

  /**
   * Handle item ended event
   */
  onItemEnded(): void {
    this.zone.run(() => {
      if (!this.carouselState.isPlaying || this.carouselState.isPaused) return;

      if (this.carouselState.currentIndex === this.playlist!.items.length - 1) {
        // Last item finished
        if (this.loop) {
          // Loop back to first item - use seamless transition
          this.logService.debug('Looping from last item back to first item');
          this.slideDirection = 'left'; // Continue forward progression for visual continuity
          this.transitionToItem(0); // Go directly to first item
        } else {
          this.handlePlaylistEnded();
        }
      } else {
        // Normal progression to next item
        this.slideDirection = 'left'; // Normal forward progression
        this.next(); // Move to next item
      }
    });
  }

  /**
   * Retry current item on error
   */
  retryCurrentItem(): void {
    if (this.currentItem) {
      this.hasError = false;
      this.loadAndPlayItem(this.carouselState.currentIndex);
    }
  }

  /**
   * Format time in MM:SS format
   */
  formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }

  private loadAndPlayItem(index: number): void {
    if (!this.playlist || index < 0 || index >= this.playlist.items.length) {
      this.handleError('Invalid item index');
      return;
    }

    const item = this.playlist.items[index];
    this.logService.info(`Loading item ${index + 1}/${this.playlist.items.length}: ${item.name}`);

    this.isLoading = true;
    this.currentItem = item;
    
    // Preload next item
    this.preloadNextItem(index);

    // Update state
    this.updateState({
      currentIndex: index,
      currentItem: item,
      nextItem: this.getNextItem(index),
      progress: 0,
      timeRemaining: item.duration || 10
    });

    // Start timers
    this.startItemTimer();
    this.startProgressTimer();
    
    this.isLoading = false;
    this.cdr.detectChanges();
  }

  private transitionToItem(index: number): void {
    if (this.isTransitioning || !this.playlist) return;

    const currentIndex = this.carouselState.currentIndex;
    this.logService.debug(`Transitioning from item ${currentIndex} to item ${index} (direction: ${this.slideDirection})`);

    this.stopAllTimers();
    this.isTransitioning = true;
    this.nextItem = this.playlist.items[index];

    // Small delay to ensure DOM is ready, then trigger change detection to start CSS animations
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 10);

    // Wait for CSS transition to complete (add small buffer to ensure animation finishes)
    this.transitionTimer = setTimeout(() => {
      this.zone.run(() => {
        this.currentItem = this.nextItem;
        this.nextItem = null;
        this.isTransitioning = false;

        this.updateState({
          currentIndex: index,
          currentItem: this.currentItem,
          nextItem: this.getNextItem(index),
          progress: 0,
          timeRemaining: this.currentItem?.duration || 10
        });

        this.startItemTimer();
        this.startProgressTimer();
        this.preloadNextItem(index);
        this.cdr.detectChanges();

        this.logService.debug(`Transition completed to item ${index}: ${this.currentItem?.name}`);
      });
    }, this.transitionDuration + 50); // Add 50ms buffer
  }

  private startItemTimer(): void {
    if (!this.currentItem) return;

    const timerConfig: TimerConfig = {
      item: this.currentItem,
      duration: this.currentItem.duration || 10,
      onComplete: () => {
        this.zone.run(() => {
          this.onItemEnded();
        });
      },
      onProgress: (progress, remaining) => {
        this.zone.run(() => {
          this.updateState({
            progress,
            timeRemaining: remaining
          });
        });
      }
    };

    this.timedPlaybackService.startTimer(timerConfig);
  }

  private startProgressTimer(): void {
    // Progress is now handled by the TimedPlaybackService
    // This method is kept for compatibility but does nothing
  }

  private preloadNextItem(currentIndex: number): void {
    const nextIndex = (currentIndex + 1) % this.playlist!.items.length;
    const nextItem = this.playlist!.items[nextIndex];
    
    if (nextItem?.content?.url) {
      this.contentSyncService.preloadContent(nextItem.content.url);
    }
  }

  private getNextItem(currentIndex: number): PlaylistItem | null {
    if (!this.playlist || this.playlist.items.length === 0) return null;
    const nextIndex = (currentIndex + 1) % this.playlist.items.length;
    return this.playlist.items[nextIndex];
  }

  private updateState(updates: Partial<CarouselState>): void {
    const newState = { ...this.carouselState, ...updates };
    this.carouselStateSubject.next(newState);
  }

  private stopAllTimers(): void {
    this.timedPlaybackService.stopTimer();
    this.stopTransitionTimer();
  }

  private stopItemTimer(): void {
    // Timer is now handled by TimedPlaybackService
    this.timedPlaybackService.stopTimer();
  }

  private stopProgressTimer(): void {
    // Progress timer is now handled by TimedPlaybackService
    // This method is kept for compatibility but does nothing
  }

  private stopTransitionTimer(): void {
    if (this.transitionTimer) {
      clearTimeout(this.transitionTimer);
      this.transitionTimer = null;
    }
  }

  private handleError(message: string): void {
    this.hasError = true;
    this.errorMessage = message;
    this.isLoading = false;
    this.stopAllTimers();
    this.updateState({ isPlaying: false });
    this.error.emit(message);
    this.logService.error(`Carousel error: ${message}`);
  }

  private handlePlaylistEnded(): void {
    this.updateState({ isPlaying: false });
    this.stopAllTimers();
    this.playlistEnded.emit();
    this.logService.info('Playlist playback completed');
  }
}
