/* carousel-demo.component.scss */
.demo-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 300;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
  }
}

.demo-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.control-group {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  h3 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    font-weight: 500;
    opacity: 0.9;
  }
  
  .btn {
    margin: 5px;
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    
    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
    
    &.primary {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    &.secondary {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    &.danger {
      background: linear-gradient(135deg, #f44336, #d32f2f);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    &.nav {
      background: linear-gradient(135deg, #2196F3, #1976D2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    &.active {
      background: linear-gradient(135deg, #FF9800, #F57C00);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.5);
      box-shadow: 0 0 20px rgba(255, 152, 0, 0.4);
    }
  }
}

.demo-status {
  margin-bottom: 30px;
}

.status-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  h3 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    font-weight: 500;
  }
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  
  .label {
    font-weight: 500;
    opacity: 0.8;
  }
  
  .value {
    font-weight: 600;
    
    &.playing {
      color: #4CAF50;
    }
    
    &.paused {
      color: #FF9800;
    }
    
    &.stopped {
      color: #f44336;
    }
  }
}

.demo-carousel {
  width: 100%;
  height: 500px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  margin-bottom: 30px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.demo-error {
  margin-bottom: 30px;
}

.error-card {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  padding: 20px;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  
  h3 {
    margin: 0 0 10px 0;
    font-size: 1.1rem;
  }
  
  p {
    margin: 0 0 15px 0;
    opacity: 0.9;
  }
  
  .btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }
  }
}

.demo-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 25px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  h3 {
    margin: 0 0 20px 0;
    font-size: 1.2rem;
    font-weight: 500;
  }
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      padding: 8px 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      
      &:last-child {
        border-bottom: none;
      }
      
      strong {
        color: #FFD700;
      }
    }
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .demo-container {
    padding: 15px;
  }
  
  .demo-header h1 {
    font-size: 2rem;
  }
  
  .demo-controls {
    grid-template-columns: 1fr;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .demo-carousel {
    height: 300px;
  }
  
  .control-group .btn {
    padding: 8px 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .demo-header h1 {
    font-size: 1.5rem;
  }
  
  .demo-carousel {
    height: 250px;
  }
  
  .control-group {
    padding: 15px;
  }
}
