<!-- player.component.html -->
<div class="player-container">
  <!-- Error overlay - shown when there's a playback error or device validation error -->
  <div *ngIf="playbackError || deviceValidationError" class="error-overlay">
    <div class="error-container">
      <div class="error-icon">
        <span class="material-icons">{{ deviceValidationError ? 'error_outline' : 'display_settings' }}</span>
      </div>
      <h2 class="error-title">{{ deviceValidationError ? 'Device Not Found' : 'Display Not Ready' }}</h2>
      <p class="error-message">
        {{ deviceValidationError ? 'This device is no longer registered in the system.' : 'No content has been assigned to this display yet.' }}
      </p>
      <p class="error-submessage">
        {{ deviceValidationError ? 'The device may have been removed by an administrator.' : 'Please check with your administrator or try the options below.' }}
      </p>
      
      <div class="error-actions">
        <button *ngIf="!deviceValidationError" class="action-button primary" (click)="reloadPlaylist()">
          <span class="material-icons">refresh</span>
          Check for Content
        </button>
        <button class="action-button secondary" (click)="resetDevice()">
          <span class="material-icons">restart_alt</span>
          Reset Device
        </button>
      </div>
    </div>
  </div>


  <!-- Main content container -->
  <div class="content-container">
    <!-- Carousel Player -->
    <app-carousel-player
      *ngIf="currentPlaylist && !deviceValidationError"
      [playlist]="currentPlaylist"
      [autoPlay]="true"
      [loop]="true"
      [transitionType]="transitionType"
      [transitionDuration]="800"
      [showProgress]="true"
      (stateChange)="onCarouselStateChange($event)"
      (playlistEnded)="onPlaylistEnded()"
      (error)="onCarouselError($event)">
    </app-carousel-player>

    <!-- Loading state when no playlist is available -->
    <div *ngIf="!currentPlaylist && !playbackError && !deviceValidationError" class="loading-state">
      <div class="loading-container">
        <div class="spinner"></div>
        <p>Loading content...</p>
      </div>
    </div>
  </div>

  <!-- Diagnostic overlay -->
  <div *ngIf="playerState$ | async as state" class="diagnostics-overlay">
    <div class="info-pill">
      <!-- <div class="status-indicator" [class.online]="isOnline" [class.offline]="!isOnline"></div>
      <span class="playlist-name">{{ state.currentPlaylistName }}</span>
      <span class="item-counter">{{ state.currentItemIndex + 1 }}/{{ state.totalItems }}</span>
      <button class="control-button refresh-button" (click)="reloadPlaylist()" title="Reload Playlist">
        <span class="material-icons">refresh</span>
      </button> -->
      

    </div>
  </div>
</div>