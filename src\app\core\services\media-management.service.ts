// media-management.service.ts
import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, forkJoin, of, from } from 'rxjs';
import { map, catchError, tap, switchMap } from 'rxjs/operators';
import { ContentSyncService } from './content-sync.service';
import { LogService } from './log.service';
import { Playlist, PlaylistItem } from '../models/playlist.model';

export interface MediaDownloadProgress {
  playlistId: string;
  playlistName: string;
  totalItems: number;
  downloadedItems: number;
  currentItem?: string;
  progress: number; // 0-100
  isComplete: boolean;
  hasErrors: boolean;
  errors: string[];
}

export interface PlaylistMediaStatus {
  playlistId: string;
  isFullyCached: boolean;
  cachedItems: number;
  totalItems: number;
  missingItems: string[];
  lastChecked: Date;
}

@Injectable({
  providedIn: 'root'
})
export class MediaManagementService {
  private downloadProgress$ = new BehaviorSubject<MediaDownloadProgress | null>(null);
  private activeDownloads = new Map<string, boolean>();

  constructor(
    private contentSyncService: ContentSyncService,
    private logService: LogService
  ) {}

  /**
   * Get current download progress observable
   */
  getDownloadProgress(): Observable<MediaDownloadProgress | null> {
    return this.downloadProgress$.asObservable();
  }

  /**
   * Download all media for a playlist with detailed progress tracking
   */
  downloadPlaylistMedia(playlist: Playlist): Observable<MediaDownloadProgress> {
    if (this.activeDownloads.has(playlist.id)) {
      this.logService.warn(`Download already in progress for playlist: ${playlist.id}`);
      return this.downloadProgress$.asObservable().pipe(
        map(progress => progress || this.createInitialProgress(playlist))
      );
    }

    this.activeDownloads.set(playlist.id, true);
    
    const initialProgress = this.createInitialProgress(playlist);
    this.downloadProgress$.next(initialProgress);

    return new Observable<MediaDownloadProgress>(observer => {
      this.contentSyncService.downloadPlaylistMedia(
        playlist,
        (progress, current, total) => {
          const updatedProgress: MediaDownloadProgress = {
            ...initialProgress,
            downloadedItems: current,
            progress: Math.round(progress),
            currentItem: current < playlist.items.length ? playlist.items[current - 1]?.name : undefined
          };
          
          this.downloadProgress$.next(updatedProgress);
          observer.next(updatedProgress);
        }
      ).subscribe({
        next: (success) => {
          const finalProgress: MediaDownloadProgress = {
            ...initialProgress,
            downloadedItems: initialProgress.totalItems,
            progress: 100,
            isComplete: true,
            hasErrors: !success,
            errors: success ? [] : ['Some media files failed to download']
          };
          
          this.downloadProgress$.next(finalProgress);
          observer.next(finalProgress);
          observer.complete();
          
          this.activeDownloads.delete(playlist.id);
          this.logService.info(`Media download completed for playlist: ${playlist.name}`);
        },
        error: (error) => {
          const errorProgress: MediaDownloadProgress = {
            ...initialProgress,
            isComplete: true,
            hasErrors: true,
            errors: [error.message || 'Download failed']
          };
          
          this.downloadProgress$.next(errorProgress);
          observer.next(errorProgress);
          observer.complete();
          
          this.activeDownloads.delete(playlist.id);
          this.logService.error(`Media download failed for playlist: ${playlist.name}`, error);
        }
      });
    });
  }

  /**
   * Download media for multiple playlists sequentially
   */
  downloadMultiplePlaylistsMedia(playlists: Playlist[]): Observable<MediaDownloadProgress[]> {
    const results: MediaDownloadProgress[] = [];
    
    return new Observable<MediaDownloadProgress[]>(observer => {
      const downloadNext = (index: number) => {
        if (index >= playlists.length) {
          observer.next(results);
          observer.complete();
          return;
        }

        const playlist = playlists[index];
        this.downloadPlaylistMedia(playlist).subscribe({
          next: (progress) => {
            // Update or add progress for this playlist
            const existingIndex = results.findIndex(r => r.playlistId === playlist.id);
            if (existingIndex >= 0) {
              results[existingIndex] = progress;
            } else {
              results.push(progress);
            }
            
            if (progress.isComplete) {
              downloadNext(index + 1);
            }
          },
          error: (error) => {
            this.logService.error(`Failed to download playlist ${playlist.name}:`, error);
            downloadNext(index + 1);
          }
        });
      };

      downloadNext(0);
    });
  }

  /**
   * Check media status for a playlist
   */
  checkPlaylistMediaStatus(playlist: Playlist): Observable<PlaylistMediaStatus> {
    return this.contentSyncService.isPlaylistFullyCached(playlist).pipe(
      map(isFullyCached => {
        const mediaUrls = this.extractMediaUrls(playlist);
        return {
          playlistId: playlist.id,
          isFullyCached,
          cachedItems: isFullyCached ? mediaUrls.length : 0, // Simplified for now
          totalItems: mediaUrls.length,
          missingItems: isFullyCached ? [] : mediaUrls, // Simplified for now
          lastChecked: new Date()
        };
      }),
      catchError(error => {
        this.logService.error(`Error checking playlist media status: ${error.message}`);
        return of({
          playlistId: playlist.id,
          isFullyCached: false,
          cachedItems: 0,
          totalItems: this.extractMediaUrls(playlist).length,
          missingItems: this.extractMediaUrls(playlist),
          lastChecked: new Date()
        });
      })
    );
  }

  /**
   * Get media readiness status for multiple playlists
   */
  checkMultiplePlaylistsStatus(playlists: Playlist[]): Observable<PlaylistMediaStatus[]> {
    const statusChecks = playlists.map(playlist => this.checkPlaylistMediaStatus(playlist));
    return forkJoin(statusChecks);
  }

  /**
   * Clear download progress
   */
  clearDownloadProgress(): void {
    this.downloadProgress$.next(null);
  }

  /**
   * Cancel active downloads (if possible)
   */
  cancelDownloads(): void {
    this.activeDownloads.clear();
    this.downloadProgress$.next(null);
  }

  private createInitialProgress(playlist: Playlist): MediaDownloadProgress {
    const mediaUrls = this.extractMediaUrls(playlist);
    return {
      playlistId: playlist.id,
      playlistName: playlist.name,
      totalItems: mediaUrls.length,
      downloadedItems: 0,
      progress: 0,
      isComplete: false,
      hasErrors: false,
      errors: []
    };
  }

  private extractMediaUrls(playlist: Playlist): string[] {
    const urls: string[] = [];
    
    playlist.items.forEach(item => {
      if (item.content?.url) {
        urls.push(item.content.url);
      }
      if (item.content?.thumbnail) {
        urls.push(item.content.thumbnail);
      }
    });

    return [...new Set(urls)]; // Remove duplicates
  }
}
